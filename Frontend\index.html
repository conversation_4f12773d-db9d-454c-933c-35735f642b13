<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TaskNest - Organize Your Life</title>
  <link rel="stylesheet" href="style.css" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Landing Page -->
  <div id="landing-page" class="page">
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2 class="logo-text">TaskNest</h2>
        </div>
        <div class="nav-buttons">
          <button class="nav-btn login-btn" onclick="showPage('login')">Login</button>
          <button class="nav-btn signup-btn" onclick="showPage('signup')">Sign Up</button>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            Organize Your Life with
            <span class="gradient-text">TaskNest</span>
          </h1>
          <p class="hero-subtitle">
            The beautiful and intuitive task management app that helps you stay productive and organized.
            Create, manage, and complete your tasks with style.
          </p>
          <div class="hero-buttons">
            <button class="cta-button primary" onclick="showPage('signup')">
              Get Started Free
              <span class="button-icon">→</span>
            </button>
            <button class="cta-button secondary" onclick="showPage('login')">
              Sign In
            </button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-card card-1">
            <div class="task-preview">
              <div class="task-check completed"></div>
              <span>Design landing page</span>
            </div>
          </div>
          <div class="floating-card card-2">
            <div class="task-preview">
              <div class="task-check"></div>
              <span>Review project proposal</span>
            </div>
          </div>
          <div class="floating-card card-3">
            <div class="task-preview">
              <div class="task-check"></div>
              <span>Team meeting at 3 PM</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="features-container">
        <h2 class="section-title">Why Choose TaskNest?</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">✨</div>
            <h3>Beautiful Interface</h3>
            <p>Clean, modern design that makes task management a pleasure</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>Lightning Fast</h3>
            <p>Instant sync and responsive interactions for seamless productivity</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔒</div>
            <h3>Secure & Private</h3>
            <p>Your data stays safe with local storage and privacy-first approach</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>Mobile Ready</h3>
            <p>Perfect experience across all devices and screen sizes</p>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Signup Page -->
  <div id="signup-page" class="page auth-page" style="display: none;">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h2>Create Account</h2>
          <p>Join TaskNest and start organizing your life</p>
        </div>
        <form id="signup-form" class="auth-form">
          <div class="form-group">
            <label for="signup-name">Full Name</label>
            <input type="text" id="signup-name" required autocomplete="name">
          </div>
          <div class="form-group">
            <label for="signup-email">Email Address</label>
            <input type="email" id="signup-email" required autocomplete="email">
          </div>
          <div class="form-group">
            <label for="signup-password">Password</label>
            <input type="password" id="signup-password" required autocomplete="new-password">
          </div>
          <div class="form-group">
            <label for="signup-confirm-password">Confirm Password</label>
            <input type="password" id="signup-confirm-password" required autocomplete="new-password">
          </div>
          <button type="submit" class="auth-button">Create Account</button>
        </form>
        <div class="auth-footer">
          <p>Already have an account? <a href="#" onclick="showPage('login')">Sign in</a></p>
          <p><a href="#" onclick="showPage('landing')">← Back to home</a></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Login Page -->
  <div id="login-page" class="page auth-page" style="display: none;">
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h2>Welcome Back</h2>
          <p>Sign in to your TaskNest account</p>
        </div>
        <form id="login-form" class="auth-form">
          <div class="form-group">
            <label for="login-email">Email Address</label>
            <input type="email" id="login-email" required autocomplete="email">
          </div>
          <div class="form-group">
            <label for="login-password">Password</label>
            <input type="password" id="login-password" required autocomplete="current-password">
          </div>
          <button type="submit" class="auth-button">Sign In</button>
        </form>
        <div class="auth-footer">
          <p>Don't have an account? <a href="#" onclick="showPage('signup')">Sign up</a></p>
          <p><a href="#" onclick="showPage('landing')">← Back to home</a></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Dashboard/Home Page -->
  <div id="dashboard-page" class="page" style="display: none;">
    <!-- Dashboard Navigation -->
    <nav class="dashboard-nav">
      <div class="nav-container">
        <div class="nav-logo">
          <h2 class="logo-text">TaskNest</h2>
        </div>
        <div class="nav-user">
          <span id="user-name" class="user-greeting"></span>
          <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
      </div>
    </nav>

    <main class="todo-app">
      <header class="app-header">
        <h1 class="gradient-text">My Tasks</h1>
        <p class="subtitle">Organize your day.</p>
      </header>

      <section class="task-input-section">
        <form id="task-form">
          <input
            type="text"
            id="task-input"
            placeholder="What needs to be done?"
            aria-label="Task input"
            required
          />
          <button type="submit" class="gradient-button">Add Task</button>
        </form>
      </section>

      <section class="task-list-section">
        <div class="empty-state" id="empty-state">
          <img src="https://i.postimg.cc/sfcJnrHX/notepad.png" alt="Notepad with pencil icon" />
          <p class="no-tasks">No tasks yet</p>
          <p class="add-prompt">Add your first task above!</p>
        </div>

        <ul id="task-list" class="task-list" hidden></ul>
      </section>
    </main>
  </div>

  <script src="script.js"></script>
</body>
</html>

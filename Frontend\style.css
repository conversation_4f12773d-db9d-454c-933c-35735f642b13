/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.signup-btn {
  position: absolute;
  top: 24px;
  right: 32px;
  z-index: 1100;
  padding: 12px 28px;
    background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
    color: #0f2027;
    border: none;
    border-radius: 20px;
    box-shadow: 0 4px 16px #00f2fe99, 0 0 0 2px #4facfe99;
    text-shadow: 0 0 8px #00f2fe, 0 0 16px #4facfe;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  transition: background 0.3s, box-shadow 0.3s;
}
.signup-btn:hover {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  color: #fff;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Or<PERSON>ron', '<PERSON><PERSON><PERSON>', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(120deg, #0f2027 0%, #2c5364 40%, #1a2980 70%, #00f2fe 100%);
  min-height: 100vh;
  color: #e0e0e0;
  box-shadow: 0 0 80px 10px #00f2fe inset;
  overflow-x: hidden;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Mobile status bar simulation */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  backdrop-filter: blur(20px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main container */
.todo-app {
  max-width: 100vw;
  width: 100%;
  margin: 0 auto;
  padding: 60px 20px 20px;
  min-height: 100vh;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header styles */
.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 20px;
}

.gradient-text {
  font-size: 2.5rem;
  font-weight: 700;
    background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
    text-shadow: 0 2px 20px #00f2fe, 0 0px 40px #4facfe;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Task input section */
.task-input-section {
  margin-bottom: 40px;
  width: 100%;
  display: flex;
  justify-content: center;
}

#task-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 320px;
  max-width: 90vw;
}

#task-input {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  color: #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  outline: none;
}

#task-input::placeholder {
  color: #999;
  font-weight: 400;
}

#task-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

/* Gradient button */
.gradient-button {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
    background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
    color: #0f2027;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px #00f2fe99, 0 0 0 2px #4facfe99;
    position: relative;
    overflow: hidden;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button:hover {
  transform: translateY(-2px);
    box-shadow: 0 12px 40px #00f2fecc, 0 0 0 2px #4facfecc;
}

.gradient-button:hover::before {
  left: 100%;
}

.gradient-button:active {
  transform: translateY(0);
}

/* Task list section */
.task-list-section {
  flex: 1;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state img {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  opacity: 0.8;
  filter: brightness(0) invert(1);
}

.no-tasks {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.add-prompt {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* Task list styles */
.task-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-item {
  background: linear-gradient(90deg, #88bed8 0%, #414345 100%);
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px #00f2fe44, 0 0 0 2px #4facfe33;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #e0e0e0;
}

.task-item:hover {
  transform: translateY(-2px);
    box-shadow: 0 8px 30px #00f2fe99, 0 0 0 2px #4facfe99;
}

.task-content {
  flex: 1;
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.complete-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.edit-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.delete-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.task-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Completed task styles */
.task-item.completed .task-content {
  text-decoration: line-through;
  opacity: 0.6;
  color: #666;
}

.task-item.completed {
  background: rgba(255, 255, 255, 0.7);
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .todo-app {
    padding: 60px 16px 20px;
  }
  
  .gradient-text {
    font-size: 2.2rem;
  }
  
  #task-input, .gradient-button {
    padding: 16px 18px;
  }
  
  .task-item {
    padding: 14px 16px;
  }
  
  .task-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* Animation for new tasks */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: slideInUp 0.3s ease-out;
}

/* Loading state */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Focus styles for accessibility */
#task-input:focus,
.gradient-button:focus,
.task-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Hidden state */
[hidden] {
  display: none !important;
}
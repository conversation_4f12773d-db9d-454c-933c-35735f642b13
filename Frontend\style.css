/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Page management */
.page {
  min-height: 100vh;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.page:not([style*="display: none"]) {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.page.fade-out {
  opacity: 0;
  transform: translateY(-20px);
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(120deg, #0f2027 0%, #2c5364 40%, #1a2980 70%, #00f2fe 100%);
  min-height: 100vh;
  color: #e0e0e0;
  overflow-x: hidden;
}

/* Landing Page Styles */
#landing-page {
  background: linear-gradient(120deg, #0f2027 0%, #2c5364 40%, #1a2980 70%, #00f2fe 100%);
  min-height: 100vh;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 32, 39, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-text {
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.nav-btn.login-btn {
  background: transparent;
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-btn.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.nav-btn.signup-btn {
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  color: #0f2027;
  font-weight: 600;
}

.nav-btn.signup-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 242, 254, 0.3);
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  max-width: 500px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.gradient-text {
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 20px #00f2fe, 0 0px 40px #4facfe;
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.cta-button.primary {
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  color: #0f2027;
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 242, 254, 0.4);
}

.cta-button.secondary {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.button-icon {
  transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
  transform: translateX(4px);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: float 6s ease-in-out infinite;
}

.floating-card.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-card.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.floating-card.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

.task-preview {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #333;
  font-weight: 500;
}

.task-check {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  position: relative;
}

.task-check.completed {
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  border-color: #00f2fe;
}

.task-check.completed::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Features Section */
.features {
  padding: 6rem 0;
  background: rgba(15, 32, 39, 0.5);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #ffffff;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 242, 254, 0.3);
  box-shadow: 0 12px 40px rgba(0, 242, 254, 0.2);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ffffff;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Authentication Pages */
.auth-page {
  background: linear-gradient(120deg, #0f2027 0%, #2c5364 40%, #1a2980 70%, #00f2fe 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  width: 100%;
  align-items: center;
}

.auth-container {
  width: 100%;
  max-width: 400px;
  justify-self: end;
}

.auth-visual {
  justify-self: start;
  width: 100%;
  max-width: 500px;
  height: 500px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.auth-header p {
  color: #666;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input {
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fff;
}

.form-group input:focus {
  outline: none;
  border-color: #00f2fe;
  box-shadow: 0 0 0 3px rgba(0, 242, 254, 0.1);
  transform: translateY(-2px);
}

.auth-button {
  padding: 1rem;
  background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  color: #0f2027;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
}

.auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 242, 254, 0.3);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-button.loading {
  position: relative;
  color: transparent;
}

.auth-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #0f2027;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Form validation styles */
.form-group.error input {
  border-color: #f44336;
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-group.error label {
  color: #f44336;
}

.error-message {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: none;
}

.form-group.error .error-message {
  display: block;
}

.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.auth-footer p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.auth-footer a {
  color: #00f2fe;
  text-decoration: none;
  font-weight: 500;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* Dashboard Navigation */
.dashboard-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 32, 39, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 0;
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-greeting {
  color: #e0e0e0;
  font-weight: 500;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Authentication Animations */
.auth-animation {
  width: 100%;
  height: 100%;
  position: relative;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.auth-element {
  position: absolute;
  animation: authFloat 6s ease-in-out infinite;
}

.auth-element.element-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.auth-element.element-2 {
  top: 30%;
  right: 15%;
  animation-delay: 1.5s;
}

.auth-element.element-3 {
  bottom: 30%;
  left: 15%;
  animation-delay: 3s;
}

.auth-element.element-4 {
  bottom: 10%;
  right: 25%;
  animation-delay: 4.5s;
}

@keyframes authFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-15px) rotate(1deg); }
  50% { transform: translateY(-10px) rotate(-1deg); }
  75% { transform: translateY(-20px) rotate(0.5deg); }
}

/* Signup Animation Elements */
.user-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00f2fe, #4facfe);
  position: relative;
  animation: pulse 2s ease-in-out infinite;
}

.avatar-circle::after {
  content: '👤';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: white;
}

.avatar-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.task-creation {
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  width: 120px;
}

.task-line {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  animation: taskLineGrow 3s ease-in-out infinite;
}

.task-line.short {
  width: 60%;
}

.plus-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #00f2fe, #4facfe);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes taskLineGrow {
  0%, 100% { width: 100%; background: #e0e0e0; }
  50% { width: 80%; background: #00f2fe; }
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.productivity-chart {
  display: flex;
  align-items: end;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-bar {
  width: 12px;
  background: linear-gradient(to top, #00f2fe, #4facfe);
  border-radius: 6px;
  animation: chartGrow 4s ease-in-out infinite;
}

.chart-bar.bar-1 {
  height: 30px;
  animation-delay: 0s;
}

.chart-bar.bar-2 {
  height: 45px;
  animation-delay: 0.5s;
}

.chart-bar.bar-3 {
  height: 25px;
  animation-delay: 1s;
}

.chart-bar.bar-4 {
  height: 40px;
  animation-delay: 1.5s;
}

@keyframes chartGrow {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.3); }
}

.notification-bell {
  position: relative;
}

.bell-body {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50% 50% 0 0;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.bell-body::after {
  content: '🔔';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.bell-dot {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  background: #ff4444;
  border-radius: 50%;
  animation: bellPulse 2s ease-in-out infinite;
}

@keyframes bellPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.3); opacity: 0.7; }
}

/* Login Animation Elements */
.login-shield {
  position: relative;
}

.shield-body {
  width: 50px;
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px 25px 0 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  animation: shieldGlow 3s ease-in-out infinite;
}

.shield-check {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #4CAF50;
  font-size: 24px;
  font-weight: bold;
  animation: checkPop 2s ease-in-out infinite;
}

@keyframes shieldGlow {
  0%, 100% { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
  50% { box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3); }
}

@keyframes checkPop {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
}

.task-list-preview {
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 140px;
}

.task-item-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.task-item-preview:last-child {
  margin-bottom: 0;
}

.task-check-preview {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  animation: taskCheck 4s ease-in-out infinite;
}

.task-item-preview.completed .task-check-preview {
  background: linear-gradient(135deg, #00f2fe, #4facfe);
  border-color: #00f2fe;
  position: relative;
}

.task-item-preview.completed .task-check-preview::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.task-text-preview {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  flex: 1;
  animation: textShimmer 3s ease-in-out infinite;
}

.task-item-preview.completed .task-text-preview {
  background: #ccc;
  position: relative;
}

.task-item-preview.completed .task-text-preview::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #999;
}

@keyframes taskCheck {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes textShimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.welcome-message {
  position: relative;
}

.message-bubble {
  background: rgba(255, 255, 255, 0.95);
  padding: 0.75rem 1rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  animation: messageFloat 4s ease-in-out infinite;
}

.message-text {
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

.message-tail {
  position: absolute;
  bottom: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.95);
}

@keyframes messageFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

.sync-animation {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sync-circle {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #00f2fe;
  border-radius: 50%;
  animation: syncRotate 2s linear infinite;
}

.sync-dots {
  position: absolute;
  display: flex;
  gap: 4px;
}

.sync-dot {
  width: 6px;
  height: 6px;
  background: #00f2fe;
  border-radius: 50%;
  animation: syncDots 1.5s ease-in-out infinite;
}

.sync-dot.dot-1 {
  animation-delay: 0s;
}

.sync-dot.dot-2 {
  animation-delay: 0.2s;
}

.sync-dot.dot-3 {
  animation-delay: 0.4s;
}

@keyframes syncRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes syncDots {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Background Shapes */
.auth-bg-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: bgFloat 8s ease-in-out infinite;
}

.bg-circle.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.bg-circle.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.bg-circle.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes bgFloat {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.05; }
  50% { transform: translateY(-30px) scale(1.1); opacity: 0.1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Main container */
.todo-app {
  max-width: 100vw;
  width: 100%;
  margin: 0 auto;
  padding: 100px 20px 20px;
  min-height: 100vh;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(120deg, #0f2027 0%, #2c5364 40%, #1a2980 70%, #00f2fe 100%);
}

/* Header styles */
.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 20px;
}

.gradient-text {
  font-size: 2.5rem;
  font-weight: 700;
    background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
    text-shadow: 0 2px 20px #00f2fe, 0 0px 40px #4facfe;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Task input section */
.task-input-section {
  margin-bottom: 40px;
  width: 100%;
  display: flex;
  justify-content: center;
}

#task-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 320px;
  max-width: 90vw;
}

#task-input {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  color: #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  outline: none;
}

#task-input::placeholder {
  color: #999;
  font-weight: 400;
}

#task-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

/* Gradient button */
.gradient-button {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
    background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
    color: #0f2027;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px #00f2fe99, 0 0 0 2px #4facfe99;
    position: relative;
    overflow: hidden;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button:hover {
  transform: translateY(-2px);
    box-shadow: 0 12px 40px #00f2fecc, 0 0 0 2px #4facfecc;
}

.gradient-button:hover::before {
  left: 100%;
}

.gradient-button:active {
  transform: translateY(0);
}

/* Task list section */
.task-list-section {
  flex: 1;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state img {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  opacity: 0.8;
  filter: brightness(0) invert(1);
}

.no-tasks {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.add-prompt {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* Task list styles */
.task-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-item {
  background: linear-gradient(90deg, #88bed8 0%, #414345 100%);
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px #00f2fe44, 0 0 0 2px #4facfe33;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #e0e0e0;
}

.task-item:hover {
  transform: translateY(-2px);
    box-shadow: 0 8px 30px #00f2fe99, 0 0 0 2px #4facfe99;
}

.task-content {
  flex: 1;
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.complete-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.edit-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.delete-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.task-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Completed task styles */
.task-item.completed .task-content {
  text-decoration: line-through;
  opacity: 0.6;
  color: #666;
}

.task-item.completed {
  background: rgba(255, 255, 255, 0.7);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-visual {
    height: 300px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .nav-buttons {
    gap: 0.5rem;
  }

  .nav-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .hero {
    padding: 6rem 0 3rem;
  }

  .hero-container {
    padding: 0 1rem;
  }

  .cta-button {
    padding: 0.9rem 1.5rem;
    font-size: 1rem;
  }

  .auth-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .auth-container {
    justify-self: center;
  }

  .auth-visual {
    justify-self: center;
    height: 300px;
    order: -1;
  }

  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .auth-element {
    transform: scale(0.8);
  }

  .task-creation {
    width: 100px;
  }

  .task-list-preview {
    width: 120px;
  }
}

@media (max-width: 480px) {
  .todo-app {
    padding: 100px 16px 20px;
  }

  .gradient-text {
    font-size: 2.2rem;
  }

  #task-input, .gradient-button {
    padding: 16px 18px;
  }

  .task-item {
    padding: 14px 16px;
  }

  .task-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .floating-card {
    padding: 0.75rem 1rem;
  }

  .task-preview {
    font-size: 0.9rem;
  }

  .auth-card {
    padding: 1.5rem;
  }

  .auth-header h2 {
    font-size: 1.5rem;
  }

  .auth-visual {
    height: 250px;
  }

  .auth-element {
    transform: scale(0.7);
  }

  .auth-layout {
    gap: 1rem;
  }

  .auth-page {
    padding: 1rem;
  }
}

/* Animation for new tasks */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: slideInUp 0.3s ease-out;
}

/* Loading state */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Focus styles for accessibility */
#task-input:focus,
.gradient-button:focus,
.task-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Hidden state */
[hidden] {
  display: none !important;
}
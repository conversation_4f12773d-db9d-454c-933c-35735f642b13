# TaskNest

## 📌 Project: To-Do List App

### 🎯 Core Features (MVP – Minimum Viable Product)

1. Add a task
2. Edit a task
3. Delete a task
4. Mark a task as completed
5. Store tasks in local storage (so data doesn’t vanish on refresh)

---

### 🚀 Optional Features (Stretch Goals)

* Categories (Work / Study / Personal)
* Deadlines for tasks
* Search & filter tasks (completed, pending)
* Dark mode toggle
* Cloud storage (Firebase / MongoDB) for login & persistence

---

### 👩‍💻 Work Division (Team of 4)

* **Member 1 (Frontend – Input & Task List UI)**

  * Create task input box + Add button
  * Display task list dynamically

* **Member 2 (Task Operations)**

  * Implement edit, delete, complete functionalities
  * Add icons/buttons for each operation

* **Member 3 (Storage & State Management)**

  * Handle localStorage (save, fetch tasks)
  * Manage app state with React useState / Context

* **Member 4 (UI/UX & Extra Features)**

  * Styling with Tailwind/Bootstrap
  * Optional: Add filters, categories, dark mode

---

### 🛠️ Tech Stack (Recommended)

* **Frontend**: HTML, CSS, JS 
* **Backend**: Express.js
* **Storage**: LocalStorage (MVP) → Firebase (Stretch goal)
* **Deployment**: Netlify / Vercel (easy CI/CD from GitHub)

---

